/**
 * Clipboard Companion - Content Script
 * Handles overlay creation, display, and interaction on web pages
 */

// Global variables
let overlayContainer = null;
let isOverlayVisible = false;
let clipboardHistory = [];
let draggedElement = null;
let stickyNote = null;

// Overlay HTML template
const OVERLAY_HTML = `
<div id="clipboard-companion-overlay" class="cc-overlay">
    <div class="cc-overlay-backdrop"></div>
    <div class="cc-overlay-content">
        <div class="cc-header">
            <h2 class="cc-title">📋 Clipboard Companion</h2>
            <button class="cc-close-btn" title="Close (Esc)">×</button>
        </div>
        <div class="cc-tag-cloud" id="cc-tag-cloud">
            <div class="cc-empty-state">
                <div class="cc-empty-icon">📋</div>
                <p>No clipboard items yet</p>
                <small>Copy some text to get started!</small>
            </div>
        </div>
    </div>
    <div class="cc-sticky-note" id="cc-sticky-note"></div>
</div>
`;

// Sticky note colors (pastel theme)
const STICKY_COLORS = [
    '#FFF9C4', // Yellow
    '#F8BBD0', // Pink
    '#B3E5FC', // Blue
    '#C8E6C9', // Green
    '#FFE0B2', // Orange
    '#E1BEE7', // Purple
    '#FFCDD2', // Light Red
    '#DCEDC8'  // Light Green
];

/**
 * Initialize content script
 */
function init() {
    console.log('Clipboard Companion content script loaded');
    
    // Load CSS if not already loaded
    loadOverlayCSS();
    
    // Listen for messages from background script
    chrome.runtime.onMessage.addListener(handleMessage);
    
    // Setup keyboard listeners
    setupKeyboardListeners();
}

/**
 * Load overlay CSS dynamically
 */
function loadOverlayCSS() {
    if (document.getElementById('clipboard-companion-css')) {
        return; // Already loaded
    }
    
    const link = document.createElement('link');
    link.id = 'clipboard-companion-css';
    link.rel = 'stylesheet';
    link.href = chrome.runtime.getURL('overlay.css');
    document.head.appendChild(link);
}

/**
 * Handle messages from background script
 */
function handleMessage(message, sender, sendResponse) {
    switch (message.action) {
        case 'ping':
            sendResponse({ status: 'alive' });
            break;
            
        case 'showOverlay':
            showOverlay();
            sendResponse({ success: true });
            break;
            
        case 'toggleOverlay':
            toggleOverlay();
            sendResponse({ success: true });
            break;
            
        case 'hideOverlay':
            hideOverlay();
            sendResponse({ success: true });
            break;
    }
}

/**
 * Setup keyboard event listeners
 */
function setupKeyboardListeners() {
    document.addEventListener('keydown', (event) => {
        // Close overlay on Escape key
        if (event.key === 'Escape' && isOverlayVisible) {
            hideOverlay();
        }
    });
}

/**
 * Show the clipboard overlay
 */
async function showOverlay() {
    if (isOverlayVisible) {
        return;
    }
    
    try {
        // Load clipboard history
        await loadClipboardHistory();
        
        // Create overlay if it doesn't exist
        if (!overlayContainer) {
            createOverlay();
        }
        
        // Populate with clipboard items
        populateTagCloud();
        
        // Show overlay
        overlayContainer.style.display = 'flex';
        isOverlayVisible = true;
        
        // Animate in
        setTimeout(() => {
            overlayContainer.classList.add('cc-visible');
        }, 10);
        
        console.log('Overlay shown with', clipboardHistory.length, 'items');
        
    } catch (error) {
        console.error('Error showing overlay:', error);
    }
}

/**
 * Hide the clipboard overlay
 */
function hideOverlay() {
    if (!isOverlayVisible || !overlayContainer) {
        return;
    }
    
    // Hide sticky note
    hideStickyNote();
    
    // Animate out
    overlayContainer.classList.remove('cc-visible');
    
    setTimeout(() => {
        overlayContainer.style.display = 'none';
        isOverlayVisible = false;
    }, 300);
    
    console.log('Overlay hidden');
}

/**
 * Toggle overlay visibility
 */
function toggleOverlay() {
    if (isOverlayVisible) {
        hideOverlay();
    } else {
        showOverlay();
    }
}

/**
 * Create the overlay DOM structure
 */
function createOverlay() {
    // Remove existing overlay if any
    const existing = document.getElementById('clipboard-companion-overlay');
    if (existing) {
        existing.remove();
    }
    
    // Create container
    overlayContainer = document.createElement('div');
    overlayContainer.innerHTML = OVERLAY_HTML;
    overlayContainer = overlayContainer.firstElementChild;
    
    // Add to page
    document.body.appendChild(overlayContainer);
    
    // Setup event listeners
    setupOverlayEventListeners();
    
    console.log('Overlay created');
}

/**
 * Setup event listeners for overlay interactions
 */
function setupOverlayEventListeners() {
    // Close button
    const closeBtn = overlayContainer.querySelector('.cc-close-btn');
    closeBtn.addEventListener('click', hideOverlay);
    
    // Backdrop click to close
    const backdrop = overlayContainer.querySelector('.cc-overlay-backdrop');
    backdrop.addEventListener('click', hideOverlay);
    
    // Prevent content clicks from closing overlay
    const content = overlayContainer.querySelector('.cc-overlay-content');
    content.addEventListener('click', (e) => e.stopPropagation());
}

/**
 * Load clipboard history from storage
 */
async function loadClipboardHistory() {
    try {
        const response = await chrome.runtime.sendMessage({ action: 'getClipboardHistory' });
        clipboardHistory = response.history || [];
        console.log('Loaded clipboard history:', clipboardHistory.length, 'items');
    } catch (error) {
        console.error('Error loading clipboard history:', error);
        clipboardHistory = [];
    }
}

/**
 * Populate the tag cloud with clipboard items
 */
function populateTagCloud() {
    const tagCloud = overlayContainer.querySelector('#cc-tag-cloud');
    
    if (clipboardHistory.length === 0) {
        tagCloud.innerHTML = `
            <div class="cc-empty-state">
                <div class="cc-empty-icon">📋</div>
                <p>No clipboard items yet</p>
                <small>Copy some text to get started!</small>
            </div>
        `;
        return;
    }
    
    // Clear existing content
    tagCloud.innerHTML = '';
    
    // Create tags for each clipboard item
    clipboardHistory.forEach((item, index) => {
        const tag = createClipboardTag(item, index);
        tagCloud.appendChild(tag);
    });
    
    console.log('Tag cloud populated with', clipboardHistory.length, 'items');
}

/**
 * Create a clipboard tag element
 */
function createClipboardTag(item, index) {
    const tag = document.createElement('div');
    tag.className = 'cc-tag';
    tag.dataset.itemId = item.id;
    tag.dataset.index = index;
    
    // Truncate content for display
    const preview = truncateText(item.content, 30);
    tag.textContent = preview;
    
    // Add event listeners
    setupTagEventListeners(tag, item);
    
    return tag;
}

/**
 * Setup event listeners for individual tags
 */
function setupTagEventListeners(tag, item) {
    // Click to paste
    tag.addEventListener('click', () => {
        pasteToActiveElement(item.content);
        hideOverlay();
    });
    
    // Hover to show sticky note
    tag.addEventListener('mouseenter', (e) => {
        showStickyNote(e, item);
    });
    
    tag.addEventListener('mouseleave', () => {
        hideStickyNote();
    });
    
    // Drag and drop
    tag.draggable = true;
    tag.addEventListener('dragstart', (e) => {
        draggedElement = tag;
        e.dataTransfer.setData('text/plain', item.content);
        e.dataTransfer.effectAllowed = 'copy';
        tag.classList.add('cc-dragging');
    });
    
    tag.addEventListener('dragend', () => {
        tag.classList.remove('cc-dragging');
        draggedElement = null;
    });
}

// Continue with remaining functions...
